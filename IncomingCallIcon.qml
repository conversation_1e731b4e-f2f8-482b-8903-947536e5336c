import QtQuick 2.12

// 呼入图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#2ecc71"
    property color arrowColor: "#27ae60"
    
    // 电话图标
    Rectangle {
        id: phone
        anchors.centerIn: parent
        width: 50
        height: 65
        color: root.primaryColor
        radius: 20
        
        // 听筒装饰
        Rectangle {
            width: 30
            height: 8
            radius: 4
            color: "white"
            opacity: 0.8
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 8
        }
        
        Rectangle {
            width: 30
            height: 8
            radius: 4
            color: "white"
            opacity: 0.8
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 8
        }
    }
    
    // 向下箭头
    Canvas {
        id: arrowDown
        anchors.horizontalCenter: phone.horizontalCenter
        anchors.top: phone.bottom
        anchors.topMargin: 5
        width: 30
        height: 20
        
        onPaint: {
            var ctx = getContext("2d")
            ctx.clearRect(0, 0, width, height)
            ctx.strokeStyle = root.arrowColor
            ctx.lineWidth = 4
            ctx.lineCap = "round"
            ctx.lineJoin = "round"
            
            // 箭头线条
            ctx.beginPath()
            ctx.moveTo(5, 5)
            ctx.lineTo(15, 15)
            ctx.lineTo(25, 5)
            ctx.stroke()
            
            // 箭头杆
            ctx.beginPath()
            ctx.moveTo(15, 0)
            ctx.lineTo(15, 12)
            ctx.stroke()
        }
    }
    
    // 动画效果
    SequentialAnimation {
        running: true
        loops: Animation.Infinite
        
        NumberAnimation {
            target: arrowDown
            property: "opacity"
            from: 1.0
            to: 0.3
            duration: 800
        }
        
        NumberAnimation {
            target: arrowDown
            property: "opacity"
            from: 0.3
            to: 1.0
            duration: 800
        }
    }
}
