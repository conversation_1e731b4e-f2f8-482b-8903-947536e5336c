import QtQuick 2.12

// 通话中图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#e74c3c"
    property color waveColor: "#c0392b"
    
    // 电话图标
    Rectangle {
        id: phone
        anchors.centerIn: parent
        width: 50
        height: 65
        color: root.primaryColor
        radius: 20
        
        // 听筒装饰
        Rectangle {
            width: 30
            height: 8
            radius: 4
            color: "white"
            opacity: 0.9
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 8
        }
        
        Rectangle {
            width: 30
            height: 8
            radius: 4
            color: "white"
            opacity: 0.9
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 8
        }
    }
    
    // 声波效果
    Row {
        anchors.left: phone.right
        anchors.leftMargin: 10
        anchors.verticalCenter: phone.verticalCenter
        spacing: 3
        
        Repeater {
            model: 4
            
            Rectangle {
                id: waveLine
                width: 3
                height: 8 + index * 6
                radius: 1.5
                color: root.waveColor
                
                SequentialAnimation {
                    running: true
                    loops: Animation.Infinite
                    
                    NumberAnimation {
                        target: waveLine
                        property: "height"
                        from: 8 + index * 6
                        to: 20 + index * 4
                        duration: 300 + index * 100
                    }
                    
                    NumberAnimation {
                        target: waveLine
                        property: "height"
                        from: 20 + index * 4
                        to: 8 + index * 6
                        duration: 300 + index * 100
                    }
                }
            }
        }
    }
    
    // 计时器显示
    Rectangle {
        width: 40
        height: 15
        radius: 7
        color: "#34495e"
        anchors.horizontalCenter: phone.horizontalCenter
        anchors.bottom: phone.top
        anchors.bottomMargin: 8
        
        Text {
            text: "00:00"
            color: "white"
            font.pixelSize: 8
            anchors.centerIn: parent
        }
    }
}
