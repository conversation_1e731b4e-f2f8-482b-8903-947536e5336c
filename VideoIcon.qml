import QtQuick 2.12

// 视频图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#3498db"
    property color secondaryColor: "#2980b9"
    
    Rectangle {
        id: videoFrame
        anchors.centerIn: parent
        width: 80
        height: 60
        color: root.primaryColor
        radius: 8
        border.width: 3
        border.color: root.secondaryColor
        
        // 播放按钮
        Rectangle {
            anchors.centerIn: parent
            width: 0
            height: 0
            
            // 三角形播放按钮
            Canvas {
                id: playButton
                anchors.centerIn: parent
                width: 24
                height: 24
                
                onPaint: {
                    var ctx = getContext("2d")
                    ctx.clearRect(0, 0, width, height)
                    ctx.fillStyle = "white"
                    ctx.beginPath()
                    ctx.moveTo(6, 4)
                    ctx.lineTo(6, 20)
                    ctx.lineTo(20, 12)
                    ctx.closePath()
                    ctx.fill()
                }
            }
        }
        
        // 装饰性线条
        Rectangle {
            width: parent.width - 16
            height: 2
            color: "white"
            opacity: 0.3
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 8
            anchors.horizontalCenter: parent.horizontalCenter
        }
    }
    
    // 底部装饰
    Row {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: videoFrame.bottom
        anchors.topMargin: 8
        spacing: 4
        
        Repeater {
            model: 3
            Rectangle {
                width: 6
                height: 6
                radius: 3
                color: index === 1 ? root.primaryColor : "#bdc3c7"
            }
        }
    }
}
