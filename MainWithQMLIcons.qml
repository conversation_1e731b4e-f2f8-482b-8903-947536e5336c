import QtQuick 2.12
import QtQuick.Window 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

ApplicationWindow {
    id: window
    visible: true
    width: 1200
    height: 800
    title: qsTr("PhoneBox Application - QML图标版本")
    
    property var iconData: [
        // 视频模块
        {
            category: "视频",
            items: [
                { name: "视频", component: "VideoIcon.qml" }
            ]
        },
        // 电话模块
        {
            category: "电话",
            items: [
                { name: "电话", component: "PhoneIcon.qml" },
                { name: "呼入", component: "IncomingCallIcon.qml" },
                { name: "通话", component: "CallInProgressIcon.qml" },
                { name: "呼出", component: "OutgoingCallIcon.qml" }
            ]
        },
        // 会议模块
        {
            category: "会议",
            items: [
                { name: "会议", component: "ConferenceIcon.qml" }
            ]
        },
        // 状态模块
        {
            category: "状态",
            items: [
                { name: "状态", component: "StatusIcon.qml" },
                { name: "图表", component: "ChartIcon.qml" },
                { name: "图表2", component: "Chart2Icon.qml" }
            ]
        },
        // 设置模块
        {
            category: "设置",
            items: [
                { name: "常用设置", component: "SettingsIcon.qml" },
                { name: "用户编辑", component: "UserEditIcon.qml" },
                { name: "账号管理", component: "AccountManagementIcon.qml" },
                { name: "网络设置", component: "NetworkSettingsIcon.qml" }
            ]
        },
        // 系统维护模块
        {
            category: "系统维护",
            items: [
                { name: "系统维护", component: "SystemMaintenanceIcon.qml" },
                { name: "维护视频设置", component: "VideoMaintenanceIcon.qml" },
                { name: "设备温度", component: "TemperatureIcon.qml" },
                { name: "其他", component: "OtherIcon.qml" }
            ]
        }
    ]
    
    ScrollView {
        anchors.fill: parent
        anchors.margins: 20
        
        Column {
            width: parent.width
            spacing: 30
            
            Text {
                text: "PhoneBox 应用 - QML控件图标展示"
                font.pixelSize: 24
                font.bold: true
                color: "#333333"
                anchors.horizontalCenter: parent.horizontalCenter
            }
            
            Text {
                text: "使用纯QML代码绘制的图标，无需外部图片文件"
                font.pixelSize: 14
                color: "#666666"
                anchors.horizontalCenter: parent.horizontalCenter
            }
            
            Repeater {
                model: iconData
                
                Column {
                    width: parent.width
                    spacing: 15
                    
                    CategoryHeader {
                        categoryName: modelData.category
                    }
                    
                    Flow {
                        width: parent.width
                        spacing: 20
                        
                        Repeater {
                            model: modelData.items
                            
                            IconCard {
                                iconName: modelData.name
                                category: parent.parent.parent.modelData.category
                                iconComponent: Qt.createComponent(modelData.component)
                                
                                onClicked: {
                                    console.log("QML图标 - 用户点击了:", iconName, "来自分类:", category)
                                    // 这里可以添加具体的功能处理
                                    showFunctionDialog(iconName, category)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 功能演示对话框
    Dialog {
        id: functionDialog
        anchors.centerIn: parent
        width: 400
        height: 300
        title: "功能演示"
        
        property string selectedFunction: ""
        property string selectedCategory: ""
        
        Column {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 15
            
            Text {
                text: "功能: " + functionDialog.selectedFunction
                font.pixelSize: 18
                font.bold: true
                color: "#2c3e50"
            }
            
            Text {
                text: "分类: " + functionDialog.selectedCategory
                font.pixelSize: 14
                color: "#7f8c8d"
            }
            
            Rectangle {
                width: parent.width
                height: 1
                color: "#ecf0f1"
            }
            
            Text {
                text: "这是一个演示界面，展示了如何使用纯QML代码创建各种功能图标。\n\n每个图标都是使用Rectangle、Canvas、Animation等QML元素绘制的，具有动态效果和交互功能。"
                font.pixelSize: 12
                color: "#34495e"
                wrapMode: Text.WordWrap
                width: parent.width
            }
            
            Row {
                anchors.horizontalCenter: parent.horizontalCenter
                spacing: 10
                
                Button {
                    text: "确定"
                    onClicked: functionDialog.close()
                }
                
                Button {
                    text: "取消"
                    onClicked: functionDialog.close()
                }
            }
        }
    }
    
    function showFunctionDialog(functionName, categoryName) {
        functionDialog.selectedFunction = functionName
        functionDialog.selectedCategory = categoryName
        functionDialog.open()
    }
}
