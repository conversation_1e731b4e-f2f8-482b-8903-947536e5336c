import QtQuick 2.12

// 图表2图标控件 (饼图)
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color color1: "#3498db"
    property color color2: "#e74c3c"
    property color color3: "#f39c12"
    property color color4: "#27ae60"
    
    // 饼图背景
    Rectangle {
        id: chartContainer
        anchors.centerIn: parent
        width: 80
        height: 80
        color: "white"
        border.width: 2
        border.color: "#34495e"
        radius: width / 2
        
        // 饼图扇形 - 使用Canvas绘制
        Canvas {
            id: pieChart
            anchors.fill: parent
            anchors.margins: 5
            
            property real startAngle: 0
            property var segments: [
                {angle: 90, color: root.color1},   // 25%
                {angle: 144, color: root.color2},  // 40%
                {angle: 72, color: root.color3},   // 20%
                {angle: 54, color: root.color4}    // 15%
            ]
            
            onPaint: {
                var ctx = getContext("2d")
                ctx.clearRect(0, 0, width, height)
                
                var centerX = width / 2
                var centerY = height / 2
                var radius = Math.min(width, height) / 2 - 2
                var currentAngle = 0
                
                for (var i = 0; i < segments.length; i++) {
                    var segment = segments[i]
                    var startAngle = currentAngle * Math.PI / 180
                    var endAngle = (currentAngle + segment.angle) * Math.PI / 180
                    
                    ctx.fillStyle = segment.color
                    ctx.beginPath()
                    ctx.moveTo(centerX, centerY)
                    ctx.arc(centerX, centerY, radius, startAngle, endAngle)
                    ctx.closePath()
                    ctx.fill()
                    
                    currentAngle += segment.angle
                }
            }
        }
        
        // 中心圆
        Rectangle {
            width: 20
            height: 20
            radius: 10
            color: "white"
            border.width: 2
            border.color: "#34495e"
            anchors.centerIn: parent
        }
    }
    
    // 图例
    Column {
        anchors.left: chartContainer.right
        anchors.leftMargin: 10
        anchors.verticalCenter: chartContainer.verticalCenter
        spacing: 3
        
        Repeater {
            model: [
                {color: root.color1, label: "A"},
                {color: root.color2, label: "B"},
                {color: root.color3, label: "C"},
                {color: root.color4, label: "D"}
            ]
            
            Row {
                spacing: 3
                
                Rectangle {
                    width: 8
                    height: 8
                    color: modelData.color
                    radius: 1
                    anchors.verticalCenter: parent.verticalCenter
                }
                
                Text {
                    text: modelData.label
                    font.pixelSize: 8
                    color: "#34495e"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
        }
    }
    
    // 标题
    Text {
        text: "分布图"
        font.pixelSize: 10
        font.bold: true
        color: "#34495e"
        anchors.horizontalCenter: chartContainer.horizontalCenter
        anchors.bottom: chartContainer.top
        anchors.bottomMargin: 8
    }
    
    // 旋转动画
    RotationAnimation {
        target: pieChart
        running: true
        loops: Animation.Infinite
        from: 0
        to: 360
        duration: 10000
    }
}
