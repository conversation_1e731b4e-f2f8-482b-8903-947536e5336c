import QtQuick 2.12

// 状态图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#f39c12"
    property color secondaryColor: "#e67e22"
    property color goodColor: "#27ae60"
    property color warningColor: "#f1c40f"
    property color errorColor: "#e74c3c"
    
    // 主显示屏
    Rectangle {
        id: monitor
        anchors.centerIn: parent
        width: 70
        height: 50
        color: "#34495e"
        radius: 5
        border.width: 3
        border.color: root.primaryColor
        
        // 屏幕内容
        Rectangle {
            anchors.fill: parent
            anchors.margins: 5
            color: "#2c3e50"
            radius: 2
            
            // 状态指示器
            Row {
                anchors.centerIn: parent
                spacing: 8
                
                Repeater {
                    model: 3
                    
                    Rectangle {
                        width: 12
                        height: 12
                        radius: 6
                        color: {
                            switch(index) {
                                case 0: return root.goodColor
                                case 1: return root.warningColor
                                case 2: return root.errorColor
                                default: return "#95a5a6"
                            }
                        }
                        
                        // 闪烁动画
                        SequentialAnimation {
                            running: true
                            loops: Animation.Infinite
                            
                            NumberAnimation {
                                target: parent
                                property: "opacity"
                                from: 1.0
                                to: 0.3
                                duration: 800 + index * 200
                            }
                            
                            NumberAnimation {
                                target: parent
                                property: "opacity"
                                from: 0.3
                                to: 1.0
                                duration: 800 + index * 200
                            }
                        }
                    }
                }
            }
        }
        
        // 显示器底座
        Rectangle {
            width: 20
            height: 8
            color: root.primaryColor
            radius: 4
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.bottom
            anchors.topMargin: 2
        }
        
        Rectangle {
            width: 40
            height: 4
            color: root.secondaryColor
            radius: 2
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.bottom
            anchors.topMargin: 8
        }
    }
    
    // 状态文本
    Text {
        text: "ONLINE"
        color: root.goodColor
        font.pixelSize: 10
        font.bold: true
        anchors.horizontalCenter: monitor.horizontalCenter
        anchors.top: monitor.bottom
        anchors.topMargin: 20
    }
    
    // 网络连接指示
    Row {
        anchors.right: monitor.right
        anchors.top: monitor.top
        anchors.margins: 5
        spacing: 1
        
        Repeater {
            model: 4
            
            Rectangle {
                width: 2
                height: 3 + index * 2
                color: index < 3 ? root.goodColor : "#95a5a6"
                opacity: index < 3 ? 1.0 : 0.3
            }
        }
    }
}
