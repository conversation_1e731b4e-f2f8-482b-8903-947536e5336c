import QtQuick 2.12

// 用户编辑图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#3498db"
    property color secondaryColor: "#2980b9"
    property color skinColor: "#f4d1ae"
    
    // 用户头像
    Rectangle {
        id: userAvatar
        anchors.centerIn: parent
        anchors.verticalCenterOffset: -10
        width: 60
        height: 60
        radius: 30
        color: root.primaryColor
        border.width: 3
        border.color: root.secondaryColor
        
        // 头部
        Rectangle {
            width: 20
            height: 20
            radius: 10
            color: root.skinColor
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 8
        }
        
        // 身体
        Rectangle {
            width: 35
            height: 25
            radius: 17
            color: root.skinColor
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 5
        }
    }
    
    // 编辑笔
    Rectangle {
        id: editPen
        anchors.right: userAvatar.right
        anchors.top: userAvatar.top
        anchors.margins: -5
        width: 20
        height: 20
        radius: 10
        color: "#e74c3c"
        border.width: 2
        border.color: "#c0392b"
        
        // 笔尖
        Rectangle {
            width: 8
            height: 12
            color: "#f39c12"
            anchors.centerIn: parent
            
            Rectangle {
                width: 2
                height: 8
                color: "#e67e22"
                anchors.centerIn: parent
            }
        }
        
        // 闪烁动画
        SequentialAnimation {
            running: true
            loops: Animation.Infinite
            
            NumberAnimation {
                target: editPen
                property: "scale"
                from: 1.0
                to: 1.2
                duration: 600
            }
            
            NumberAnimation {
                target: editPen
                property: "scale"
                from: 1.2
                to: 1.0
                duration: 600
            }
        }
    }
    
    // 编辑线条
    Column {
        anchors.horizontalCenter: userAvatar.horizontalCenter
        anchors.top: userAvatar.bottom
        anchors.topMargin: 10
        spacing: 3
        
        Repeater {
            model: 3
            
            Rectangle {
                width: 40 - index * 8
                height: 2
                radius: 1
                color: root.secondaryColor
                opacity: 0.7
                anchors.horizontalCenter: parent.horizontalCenter
                
                SequentialAnimation {
                    running: true
                    loops: Animation.Infinite
                    
                    NumberAnimation {
                        target: parent
                        property: "opacity"
                        from: 0.7
                        to: 0.2
                        duration: 800 + index * 200
                    }
                    
                    NumberAnimation {
                        target: parent
                        property: "opacity"
                        from: 0.2
                        to: 0.7
                        duration: 800 + index * 200
                    }
                }
            }
        }
    }
    
    // 用户状态指示
    Rectangle {
        width: 8
        height: 8
        radius: 4
        color: "#27ae60"
        anchors.right: userAvatar.right
        anchors.bottom: userAvatar.bottom
        border.width: 2
        border.color: "white"
    }
}
