import QtQuick 2.12

// 系统维护图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#e67e22"
    property color secondaryColor: "#d35400"
    property color toolColor: "#95a5a6"
    
    // 系统主机
    Rectangle {
        id: systemBox
        anchors.centerIn: parent
        width: 60
        height: 70
        color: root.primaryColor
        radius: 8
        border.width: 2
        border.color: root.secondaryColor
        
        // 系统面板
        Rectangle {
            anchors.fill: parent
            anchors.margins: 6
            color: "#2c3e50"
            radius: 4
            
            // 状态指示灯
            Row {
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: parent.top
                anchors.topMargin: 6
                spacing: 4
                
                Repeater {
                    model: 3
                    
                    Rectangle {
                        width: 6
                        height: 6
                        radius: 3
                        color: {
                            switch(index) {
                                case 0: return "#27ae60"  // 绿色 - 正常
                                case 1: return "#f1c40f"  // 黄色 - 警告
                                case 2: return "#e74c3c"  // 红色 - 错误
                            }
                        }
                        
                        SequentialAnimation {
                            running: true
                            loops: Animation.Infinite
                            
                            NumberAnimation {
                                target: parent
                                property: "opacity"
                                from: 1.0
                                to: 0.3
                                duration: 1000 + index * 300
                            }
                            
                            NumberAnimation {
                                target: parent
                                property: "opacity"
                                from: 0.3
                                to: 1.0
                                duration: 1000 + index * 300
                            }
                        }
                    }
                }
            }
            
            // 系统信息显示
            Column {
                anchors.centerIn: parent
                spacing: 3
                
                Repeater {
                    model: 4
                    
                    Rectangle {
                        width: 35 - index * 3
                        height: 2
                        radius: 1
                        color: "#ecf0f1"
                        opacity: 0.8
                        anchors.horizontalCenter: parent.horizontalCenter
                    }
                }
            }
        }
        
        // 散热孔
        Grid {
            anchors.right: parent.right
            anchors.verticalCenter: parent.verticalCenter
            anchors.rightMargin: 3
            columns: 2
            spacing: 2
            
            Repeater {
                model: 6
                Rectangle {
                    width: 3
                    height: 8
                    radius: 1.5
                    color: root.secondaryColor
                }
            }
        }
    }
    
    // 维护工具 - 扳手
    Rectangle {
        id: wrench
        anchors.right: systemBox.right
        anchors.top: systemBox.top
        anchors.margins: -10
        width: 25
        height: 8
        color: root.toolColor
        radius: 4
        rotation: 45
        
        Rectangle {
            width: 8
            height: 8
            radius: 4
            color: root.toolColor
            anchors.right: parent.right
            anchors.verticalCenter: parent.verticalCenter
        }
        
        Rectangle {
            width: 6
            height: 6
            radius: 3
            color: "#7f8c8d"
            anchors.left: parent.left
            anchors.verticalCenter: parent.verticalCenter
            anchors.leftMargin: 2
        }
    }
    
    // 维护工具 - 螺丝刀
    Rectangle {
        id: screwdriver
        anchors.left: systemBox.left
        anchors.top: systemBox.top
        anchors.margins: -8
        width: 3
        height: 20
        color: "#f39c12"
        radius: 1.5
        rotation: -30
        
        Rectangle {
            width: 6
            height: 6
            radius: 3
            color: "#e67e22"
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
        }
    }
    
    // 维护进度指示
    Rectangle {
        width: systemBox.width
        height: 8
        color: "#ecf0f1"
        radius: 4
        anchors.horizontalCenter: systemBox.horizontalCenter
        anchors.top: systemBox.bottom
        anchors.topMargin: 10
        
        Rectangle {
            id: progressBar
            width: 0
            height: parent.height
            color: "#3498db"
            radius: 4
            
            SequentialAnimation {
                running: true
                loops: Animation.Infinite
                
                NumberAnimation {
                    target: progressBar
                    property: "width"
                    from: 0
                    to: systemBox.width
                    duration: 2000
                }
                
                NumberAnimation {
                    target: progressBar
                    property: "width"
                    from: systemBox.width
                    to: 0
                    duration: 500
                }
                
                PauseAnimation {
                    duration: 1000
                }
            }
        }
    }
}
