import QtQuick 2.12
import QtQuick.Window 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

ApplicationWindow {
    id: window
    visible: true
    width: 1200
    height: 800
    title: qsTr("PhoneBox Application")

    property var imageData: [
        // 视频模块
        {
            category: "视频",
            items: [
                { name: "视频", source: "qrc:/PhoneBoxAssets/01-01-视频.png" }
            ]
        },
        // 电话模块
        {
            category: "电话",
            items: [
                { name: "电话", source: "qrc:/PhoneBoxAssets/02-01-电话.png" },
                { name: "呼入", source: "qrc:/PhoneBoxAssets/02-02-呼入.png" },
                { name: "通话", source: "qrc:/PhoneBoxAssets/02-03-通话.png" },
                { name: "呼出", source: "qrc:/PhoneBoxAssets/02-04-呼出.png" }
            ]
        },
        // 会议模块
        {
            category: "会议",
            items: [
                { name: "会议", source: "qrc:/PhoneBoxAssets/03-01-会议.png" }
            ]
        },
        // 状态模块
        {
            category: "状态",
            items: [
                { name: "状态", source: "qrc:/PhoneBoxAssets/04-01-状态.png" },
                { name: "图表", source: "qrc:/PhoneBoxAssets/04-02-图表.png" },
                { name: "图表2", source: "qrc:/PhoneBoxAssets/04-03-图表2.png" }
            ]
        },
        // 设置模块
        {
            category: "设置",
            items: [
                { name: "常用设置", source: "qrc:/PhoneBoxAssets/08-01-常用设置.png" },
                { name: "用户编辑", source: "qrc:/PhoneBoxAssets/08-02-用户编辑.png" },
                { name: "账号管理", source: "qrc:/PhoneBoxAssets/08-03-账号管理.png" },
                { name: "网络设置", source: "qrc:/PhoneBoxAssets/08-04-网络设置.png" }
            ]
        },
        // 系统维护模块
        {
            category: "系统维护",
            items: [
                { name: "系统维护", source: "qrc:/PhoneBoxAssets/08-05-01-系统维护.png" },
                { name: "维护视频设置", source: "qrc:/PhoneBoxAssets/08-05-02-维护视频设置.png" },
                { name: "设备温度", source: "qrc:/PhoneBoxAssets/08-05-04-设备温度.png" },
                { name: "其他", source: "qrc:/PhoneBoxAssets/08-05-05-其他.png" }
            ]
        }
    ]

    ScrollView {
        anchors.fill: parent
        anchors.margins: 20

        Column {
            width: parent.width
            spacing: 30

            Text {
                text: "PhoneBox 应用图标展示"
                font.pixelSize: 24
                font.bold: true
                color: "#333333"
                anchors.horizontalCenter: parent.horizontalCenter
            }

            Repeater {
                model: imageData

                Column {
                    width: parent.width
                    spacing: 15

                    Rectangle {
                        width: parent.width
                        height: 40
                        color: "#f0f0f0"
                        radius: 5

                        Text {
                            text: modelData.category
                            font.pixelSize: 18
                            font.bold: true
                            color: "#2c3e50"
                            anchors.centerIn: parent
                        }
                    }

                    Flow {
                        width: parent.width
                        spacing: 20

                        Repeater {
                            model: modelData.items

                            Rectangle {
                                width: 200
                                height: 200
                                color: "#ffffff"
                                border.color: "#e0e0e0"
                                border.width: 1
                                radius: 8

                                Column {
                                    anchors.centerIn: parent
                                    spacing: 10

                                    Image {
                                        id: iconImage
                                        source: modelData.source
                                        width: 120
                                        height: 120
                                        fillMode: Image.PreserveAspectFit
                                        anchors.horizontalCenter: parent.horizontalCenter

                                        Rectangle {
                                            anchors.fill: parent
                                            color: "transparent"
                                            border.color: iconImage.status === Image.Error ? "red" : "transparent"
                                            border.width: 2
                                        }

                                        Text {
                                            visible: iconImage.status === Image.Error
                                            text: "加载失败"
                                            color: "red"
                                            anchors.centerIn: parent
                                        }
                                    }

                                    Text {
                                        text: modelData.name
                                        font.pixelSize: 14
                                        color: "#555555"
                                        anchors.horizontalCenter: parent.horizontalCenter
                                        wrapMode: Text.WordWrap
                                        width: 180
                                        horizontalAlignment: Text.AlignHCenter
                                    }
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    hoverEnabled: true

                                    onEntered: {
                                        parent.color = "#f8f9fa"
                                        parent.border.color = "#007bff"
                                    }

                                    onExited: {
                                        parent.color = "#ffffff"
                                        parent.border.color = "#e0e0e0"
                                    }

                                    onClicked: {
                                        console.log("点击了:", modelData.name)
                                        // 这里可以添加点击事件处理
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
