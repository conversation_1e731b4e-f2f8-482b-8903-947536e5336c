# PhoneBox QML 应用说明

本项目基于 PhoneBoxAssets 目录中的图片资源生成了相应的 QML 代码，用于展示电话盒子应用的各种功能图标。

## 文件结构

### 主要 QML 文件

1. **main.qml** - 主界面文件，包含完整的分类展示布局
2. **MainSimple.qml** - 简化版主界面，使用组件化设计
3. **MainGrid.qml** - 网格布局版本，所有图标以网格形式展示

### 组件文件

1. **IconCard.qml** - 图标卡片组件
   - 显示图片和名称
   - 支持鼠标悬停效果
   - 包含点击事件处理
   - 错误状态显示

2. **CategoryHeader.qml** - 分类标题组件
   - 显示分类名称
   - 带有装饰性边框和分割线

### 资源文件

- **qml.qrc** - Qt 资源文件，包含所有 QML 文件和图片资源

## 图片资源分类

### 1. 视频模块 (01-xx)
- 01-01-视频.png

### 2. 电话模块 (02-xx)
- 02-01-电话.png
- 02-02-呼入.png
- 02-03-通话.png
- 02-04-呼出.png

### 3. 会议模块 (03-xx)
- 03-01-会议.png

### 4. 状态模块 (04-xx)
- 04-01-状态.png
- 04-02-图表.png
- 04-03-图表2.png

### 5. 设置模块 (08-01 到 08-04)
- 08-01-常用设置.png
- 08-02-用户编辑.png
- 08-03-账号管理.png
- 08-04-网络设置.png

### 6. 系统维护模块 (08-05-xx)
- 08-05-01-系统维护.png
- 08-05-02-维护视频设置.png
- 08-05-04-设备温度.png
- 08-05-05-其他.png

## 功能特性

### IconCard 组件特性
- **图片加载状态处理**: 显示加载中、加载失败状态
- **交互效果**: 鼠标悬停时的缩放和颜色变化动画
- **点击事件**: 支持自定义点击处理
- **响应式设计**: 自适应不同尺寸

### 布局特性
- **分类展示**: 按功能模块分组显示
- **流式布局**: 自动换行适应窗口大小
- **网格布局**: 统一的网格排列
- **滚动支持**: 内容超出时支持滚动

## 使用方法

1. **编译运行**:
   ```bash
   mkdir build
   cd build
   cmake ..
   make
   ./PhoneBoxCursor
   ```

2. **切换不同界面**:
   - 修改 main.cpp 中的 QML 文件路径
   - 或者在运行时动态加载不同的 QML 文件

3. **自定义功能**:
   - 在 IconCard 的 onClicked 事件中添加具体功能
   - 修改样式和布局参数
   - 添加新的图标和分类

## 扩展建议

1. **添加导航**: 实现不同模块间的导航功能
2. **状态管理**: 添加应用状态管理
3. **动画效果**: 增加页面切换动画
4. **主题支持**: 实现深色/浅色主题切换
5. **国际化**: 添加多语言支持

## 技术栈

- Qt 5.12+
- QML
- Qt Quick Controls 2
- Qt Quick Layouts
