import QtQuick 2.12

// 会议图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#9b59b6"
    property color secondaryColor: "#8e44ad"
    
    // 会议桌
    Rectangle {
        id: table
        anchors.centerIn: parent
        anchors.verticalCenterOffset: 10
        width: 80
        height: 40
        color: root.secondaryColor
        radius: 20
        
        // 桌面装饰
        Rectangle {
            width: parent.width - 10
            height: parent.height - 10
            radius: 15
            color: root.primaryColor
            anchors.centerIn: parent
        }
    }
    
    // 人员图标
    Row {
        anchors.horizontalCenter: table.horizontalCenter
        anchors.bottom: table.top
        anchors.bottomMargin: 5
        spacing: 15
        
        Repeater {
            model: 3
            
            Column {
                spacing: 2
                
                // 头部
                Rectangle {
                    width: 12
                    height: 12
                    radius: 6
                    color: index === 1 ? "#e74c3c" : root.primaryColor
                    anchors.horizontalCenter: parent.horizontalCenter
                }
                
                // 身体
                Rectangle {
                    width: 16
                    height: 20
                    radius: 8
                    color: index === 1 ? "#c0392b" : root.secondaryColor
                    anchors.horizontalCenter: parent.horizontalCenter
                }
            }
        }
    }
    
    // 麦克风
    Rectangle {
        id: microphone
        width: 8
        height: 15
        radius: 4
        color: "#34495e"
        anchors.horizontalCenter: table.horizontalCenter
        anchors.verticalCenter: table.verticalCenter
        
        Rectangle {
            width: 2
            height: 8
            color: "#34495e"
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.bottom
        }
        
        Rectangle {
            width: 12
            height: 2
            radius: 1
            color: "#34495e"
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.bottom
            anchors.topMargin: 6
        }
    }
    
    // 声音波纹
    Repeater {
        model: 2
        
        Rectangle {
            width: microphone.width + (index + 1) * 10
            height: microphone.height + (index + 1) * 8
            radius: width / 2
            color: "transparent"
            border.width: 1
            border.color: "#95a5a6"
            opacity: 0.5 - index * 0.2
            anchors.centerIn: microphone
            
            SequentialAnimation {
                running: true
                loops: Animation.Infinite
                
                NumberAnimation {
                    target: parent
                    property: "opacity"
                    from: 0.5 - index * 0.2
                    to: 0.1
                    duration: 1500
                }
                
                NumberAnimation {
                    target: parent
                    property: "opacity"
                    from: 0.1
                    to: 0.5 - index * 0.2
                    duration: 1500
                }
            }
        }
    }
}
