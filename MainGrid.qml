import QtQuick 2.12
import QtQuick.Window 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

ApplicationWindow {
    id: window
    visible: true
    width: 1200
    height: 800
    title: qsTr("PhoneBox Application - 网格布局")
    
    property var allIcons: [
        { name: "视频", source: "qrc:/PhoneBoxAssets/01-01-视频.png", category: "视频" },
        { name: "电话", source: "qrc:/PhoneBoxAssets/02-01-电话.png", category: "电话" },
        { name: "呼入", source: "qrc:/PhoneBoxAssets/02-02-呼入.png", category: "电话" },
        { name: "通话", source: "qrc:/PhoneBoxAssets/02-03-通话.png", category: "电话" },
        { name: "呼出", source: "qrc:/PhoneBoxAssets/02-04-呼出.png", category: "电话" },
        { name: "会议", source: "qrc:/PhoneBoxAssets/03-01-会议.png", category: "会议" },
        { name: "状态", source: "qrc:/PhoneBoxAssets/04-01-状态.png", category: "状态" },
        { name: "图表", source: "qrc:/PhoneBoxAssets/04-02-图表.png", category: "状态" },
        { name: "图表2", source: "qrc:/PhoneBoxAssets/04-03-图表2.png", category: "状态" },
        { name: "常用设置", source: "qrc:/PhoneBoxAssets/08-01-常用设置.png", category: "设置" },
        { name: "用户编辑", source: "qrc:/PhoneBoxAssets/08-02-用户编辑.png", category: "设置" },
        { name: "账号管理", source: "qrc:/PhoneBoxAssets/08-03-账号管理.png", category: "设置" },
        { name: "网络设置", source: "qrc:/PhoneBoxAssets/08-04-网络设置.png", category: "设置" },
        { name: "系统维护", source: "qrc:/PhoneBoxAssets/08-05-01-系统维护.png", category: "系统维护" },
        { name: "维护视频设置", source: "qrc:/PhoneBoxAssets/08-05-02-维护视频设置.png", category: "系统维护" },
        { name: "设备温度", source: "qrc:/PhoneBoxAssets/08-05-04-设备温度.png", category: "系统维护" },
        { name: "其他", source: "qrc:/PhoneBoxAssets/08-05-05-其他.png", category: "系统维护" }
    ]
    
    ScrollView {
        anchors.fill: parent
        anchors.margins: 20
        
        Column {
            width: parent.width
            spacing: 20
            
            Text {
                text: "PhoneBox 应用图标展示 - 网格布局"
                font.pixelSize: 24
                font.bold: true
                color: "#333333"
                anchors.horizontalCenter: parent.horizontalCenter
            }
            
            GridLayout {
                width: parent.width
                columns: Math.floor(parent.width / 220) // 动态计算列数
                columnSpacing: 20
                rowSpacing: 20
                
                Repeater {
                    model: allIcons
                    
                    IconCard {
                        Layout.preferredWidth: 200
                        Layout.preferredHeight: 200
                        iconSource: modelData.source
                        iconName: modelData.name
                        category: modelData.category
                        
                        onClicked: {
                            console.log("网格布局 - 用户点击了:", iconName, "来自分类:", category)
                            // 这里可以添加具体的功能处理
                        }
                    }
                }
            }
        }
    }
}
