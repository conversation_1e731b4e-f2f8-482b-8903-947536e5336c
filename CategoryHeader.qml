import QtQuick 2.12

Rectangle {
    id: root
    
    property string categoryName: ""
    property color backgroundColor: "#f0f0f0"
    property color textColor: "#2c3e50"
    
    width: parent.width
    height: 50
    color: backgroundColor
    radius: 8
    
    Rectangle {
        width: 4
        height: parent.height - 10
        color: "#007bff"
        anchors.left: parent.left
        anchors.leftMargin: 15
        anchors.verticalCenter: parent.verticalCenter
        radius: 2
    }
    
    Text {
        text: root.categoryName
        font.pixelSize: 18
        font.bold: true
        color: root.textColor
        anchors.left: parent.left
        anchors.leftMargin: 30
        anchors.verticalCenter: parent.verticalCenter
    }
    
    Rectangle {
        width: parent.width - 60
        height: 1
        color: "#d0d0d0"
        anchors.bottom: parent.bottom
        anchors.horizontalCenter: parent.horizontalCenter
    }
}
