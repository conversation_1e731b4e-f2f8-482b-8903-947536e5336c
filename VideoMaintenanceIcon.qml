import QtQuick 2.12

// 维护视频设置图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#9b59b6"
    property color secondaryColor: "#8e44ad"
    property color toolColor: "#f39c12"
    
    // 视频显示器
    Rectangle {
        id: monitor
        anchors.centerIn: parent
        width: 70
        height: 50
        color: "#34495e"
        radius: 5
        border.width: 3
        border.color: root.primaryColor
        
        // 屏幕
        Rectangle {
            anchors.fill: parent
            anchors.margins: 5
            color: "#2c3e50"
            radius: 2
            
            // 视频播放界面
            Rectangle {
                anchors.fill: parent
                anchors.margins: 3
                color: root.primaryColor
                radius: 1
                
                // 播放按钮
                Canvas {
                    anchors.centerIn: parent
                    width: 20
                    height: 20
                    
                    onPaint: {
                        var ctx = getContext("2d")
                        ctx.clearRect(0, 0, width, height)
                        ctx.fillStyle = "white"
                        ctx.beginPath()
                        ctx.moveTo(6, 4)
                        ctx.lineTo(6, 16)
                        ctx.lineTo(16, 10)
                        ctx.closePath()
                        ctx.fill()
                    }
                }
                
                // 视频质量指示
                Row {
                    anchors.right: parent.right
                    anchors.top: parent.top
                    anchors.margins: 3
                    spacing: 1
                    
                    Repeater {
                        model: 4
                        Rectangle {
                            width: 2
                            height: 2 + index
                            color: index < 3 ? "#27ae60" : "#95a5a6"
                            opacity: index < 3 ? 1.0 : 0.5
                        }
                    }
                }
            }
        }
        
        // 显示器底座
        Rectangle {
            width: 20
            height: 6
            color: root.primaryColor
            radius: 3
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.bottom
            anchors.topMargin: 2
        }
        
        Rectangle {
            width: 35
            height: 3
            color: root.secondaryColor
            radius: 1.5
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.bottom
            anchors.topMargin: 6
        }
    }
    
    // 维护工具图标
    Rectangle {
        id: toolIcon
        anchors.right: monitor.right
        anchors.top: monitor.top
        anchors.margins: -8
        width: 20
        height: 20
        radius: 10
        color: root.toolColor
        border.width: 2
        border.color: "#e67e22"
        
        // 工具符号
        Canvas {
            anchors.centerIn: parent
            width: 12
            height: 12
            
            onPaint: {
                var ctx = getContext("2d")
                ctx.clearRect(0, 0, width, height)
                ctx.strokeStyle = "white"
                ctx.lineWidth = 2
                ctx.lineCap = "round"
                
                // 扳手形状
                ctx.beginPath()
                ctx.moveTo(2, 2)
                ctx.lineTo(10, 10)
                ctx.moveTo(2, 10)
                ctx.lineTo(10, 2)
                ctx.stroke()
            }
        }
        
        // 工具闪烁动画
        SequentialAnimation {
            running: true
            loops: Animation.Infinite
            
            NumberAnimation {
                target: toolIcon
                property: "scale"
                from: 1.0
                to: 1.3
                duration: 800
            }
            
            NumberAnimation {
                target: toolIcon
                property: "scale"
                from: 1.3
                to: 1.0
                duration: 800
            }
        }
    }
    
    // 设置选项
    Column {
        anchors.left: monitor.right
        anchors.leftMargin: 10
        anchors.verticalCenter: monitor.verticalCenter
        spacing: 4
        
        Repeater {
            model: 3
            
            Row {
                spacing: 3
                
                Rectangle {
                    width: 6
                    height: 6
                    radius: 3
                    color: index === 1 ? root.primaryColor : "#bdc3c7"
                    anchors.verticalCenter: parent.verticalCenter
                }
                
                Rectangle {
                    width: 15
                    height: 2
                    radius: 1
                    color: "#ecf0f1"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
        }
    }
    
    // 信号强度指示
    Row {
        anchors.left: monitor.left
        anchors.bottom: monitor.bottom
        anchors.margins: 5
        spacing: 1
        
        Repeater {
            model: 4
            Rectangle {
                width: 2
                height: 3 + index * 2
                color: index < 2 ? "#e74c3c" : (index < 3 ? "#f1c40f" : "#27ae60")
                opacity: index < 2 ? 1.0 : 0.6
            }
        }
    }
}
