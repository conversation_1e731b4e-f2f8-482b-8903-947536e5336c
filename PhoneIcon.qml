import QtQuick 2.12

// 电话图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#27ae60"
    property color secondaryColor: "#229954"
    
    // 电话听筒
    Rectangle {
        id: phoneBody
        anchors.centerIn: parent
        width: 60
        height: 80
        color: root.primaryColor
        radius: 25
        
        // 听筒顶部
        Rectangle {
            width: 40
            height: 15
            radius: 7
            color: root.secondaryColor
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 8
        }
        
        // 听筒底部
        Rectangle {
            width: 40
            height: 15
            radius: 7
            color: root.secondaryColor
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 8
        }
        
        // 中间装饰线
        Column {
            anchors.centerIn: parent
            spacing: 3
            
            Repeater {
                model: 3
                Rectangle {
                    width: 30
                    height: 2
                    radius: 1
                    color: "white"
                    opacity: 0.7
                }
            }
        }
    }
    
    // 信号波纹效果
    Repeater {
        model: 3
        
        Rectangle {
            width: phoneBody.width + (index + 1) * 20
            height: phoneBody.height + (index + 1) * 20
            radius: width / 2
            color: "transparent"
            border.width: 2
            border.color: root.primaryColor
            opacity: 0.3 - index * 0.1
            anchors.centerIn: phoneBody
        }
    }
}
