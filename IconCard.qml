import QtQuick 2.12
import QtQuick.Controls 2.12

Rectangle {
    id: root

    property string iconSource: ""
    property string iconName: ""
    property string category: ""
    property Component iconComponent: null

    signal clicked()
    
    width: 200
    height: 200
    color: "#ffffff"
    border.color: "#e0e0e0"
    border.width: 1
    radius: 8
    
    Column {
        anchors.centerIn: parent
        spacing: 10
        
        // 图标容器 - 支持QML组件或图片
        Item {
            id: iconContainer
            width: 120
            height: 120
            anchors.horizontalCenter: parent.horizontalCenter

            // QML组件加载器
            Loader {
                id: componentLoader
                anchors.fill: parent
                sourceComponent: root.iconComponent
                visible: root.iconComponent !== null
            }

            // 图片加载器（备用）
            Image {
                id: iconImage
                anchors.fill: parent
                source: root.iconSource
                fillMode: Image.PreserveAspectFit
                visible: root.iconComponent === null && root.iconSource !== ""

                Rectangle {
                    anchors.fill: parent
                    color: "transparent"
                    border.color: iconImage.status === Image.Error ? "red" : "transparent"
                    border.width: 2
                    radius: 4
                }

                Text {
                    visible: iconImage.status === Image.Error
                    text: "图片加载失败"
                    color: "red"
                    font.pixelSize: 12
                    anchors.centerIn: parent
                    wrapMode: Text.WordWrap
                    width: parent.width - 10
                    horizontalAlignment: Text.AlignHCenter
                }

                Text {
                    visible: iconImage.status === Image.Loading
                    text: "加载中..."
                    color: "#666666"
                    font.pixelSize: 12
                    anchors.centerIn: parent
                }
            }

            // 默认占位符
            Rectangle {
                anchors.fill: parent
                color: "#f8f9fa"
                border.color: "#dee2e6"
                border.width: 2
                radius: 8
                visible: root.iconComponent === null && root.iconSource === ""

                Text {
                    text: "?"
                    font.pixelSize: 48
                    color: "#6c757d"
                    anchors.centerIn: parent
                }
            }
        }
        
        Text {
            text: root.iconName
            font.pixelSize: 14
            color: "#555555"
            anchors.horizontalCenter: parent.horizontalCenter
            wrapMode: Text.WordWrap
            width: 180
            horizontalAlignment: Text.AlignHCenter
        }
    }
    
    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        
        onEntered: {
            root.color = "#f8f9fa"
            root.border.color = "#007bff"
            root.scale = 1.02
        }
        
        onExited: {
            root.color = "#ffffff"
            root.border.color = "#e0e0e0"
            root.scale = 1.0
        }
        
        onClicked: {
            console.log("点击了图标:", root.iconName, "分类:", root.category)
            root.clicked()
        }
    }
    
    Behavior on scale {
        NumberAnimation {
            duration: 150
            easing.type: Easing.OutQuad
        }
    }
    
    Behavior on color {
        ColorAnimation {
            duration: 150
        }
    }
    
    Behavior on border.color {
        ColorAnimation {
            duration: 150
        }
    }
}
