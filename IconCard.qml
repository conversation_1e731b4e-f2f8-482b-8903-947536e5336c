import QtQuick 2.12
import QtQuick.Controls 2.12

Rectangle {
    id: root
    
    property string iconSource: ""
    property string iconName: ""
    property string category: ""
    
    signal clicked()
    
    width: 200
    height: 200
    color: "#ffffff"
    border.color: "#e0e0e0"
    border.width: 1
    radius: 8
    
    Column {
        anchors.centerIn: parent
        spacing: 10
        
        Image {
            id: iconImage
            source: root.iconSource
            width: 120
            height: 120
            fillMode: Image.PreserveAspectFit
            anchors.horizontalCenter: parent.horizontalCenter
            
            Rectangle {
                anchors.fill: parent
                color: "transparent"
                border.color: iconImage.status === Image.Error ? "red" : "transparent"
                border.width: 2
                radius: 4
            }
            
            Text {
                visible: iconImage.status === Image.Error
                text: "图片加载失败"
                color: "red"
                font.pixelSize: 12
                anchors.centerIn: parent
                wrapMode: Text.WordWrap
                width: parent.width - 10
                horizontalAlignment: Text.AlignHCenter
            }
            
            Text {
                visible: iconImage.status === Image.Loading
                text: "加载中..."
                color: "#666666"
                font.pixelSize: 12
                anchors.centerIn: parent
            }
        }
        
        Text {
            text: root.iconName
            font.pixelSize: 14
            color: "#555555"
            anchors.horizontalCenter: parent.horizontalCenter
            wrapMode: Text.WordWrap
            width: 180
            horizontalAlignment: Text.AlignHCenter
        }
    }
    
    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        
        onEntered: {
            root.color = "#f8f9fa"
            root.border.color = "#007bff"
            root.scale = 1.02
        }
        
        onExited: {
            root.color = "#ffffff"
            root.border.color = "#e0e0e0"
            root.scale = 1.0
        }
        
        onClicked: {
            console.log("点击了图标:", root.iconName, "分类:", root.category)
            root.clicked()
        }
    }
    
    Behavior on scale {
        NumberAnimation {
            duration: 150
            easing.type: Easing.OutQuad
        }
    }
    
    Behavior on color {
        ColorAnimation {
            duration: 150
        }
    }
    
    Behavior on border.color {
        ColorAnimation {
            duration: 150
        }
    }
}
