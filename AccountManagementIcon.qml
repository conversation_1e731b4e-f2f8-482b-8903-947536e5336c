import QtQuick 2.12

// 账号管理图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#2c3e50"
    property color secondaryColor: "#34495e"
    property color accentColor: "#3498db"
    
    // 文件夹
    Rectangle {
        id: folder
        anchors.centerIn: parent
        width: 70
        height: 50
        color: root.primaryColor
        radius: 5
        
        // 文件夹标签
        Rectangle {
            width: 20
            height: 8
            color: root.primaryColor
            radius: 2
            anchors.left: parent.left
            anchors.bottom: parent.top
            anchors.leftMargin: 8
        }
        
        // 文件夹内容
        Rectangle {
            anchors.fill: parent
            anchors.margins: 5
            color: root.secondaryColor
            radius: 3
            
            // 用户列表
            Column {
                anchors.centerIn: parent
                spacing: 4
                
                Repeater {
                    model: 3
                    
                    Row {
                        spacing: 6
                        
                        // 用户头像
                        Rectangle {
                            width: 12
                            height: 12
                            radius: 6
                            color: index === 0 ? root.accentColor : "#95a5a6"
                            
                            Rectangle {
                                width: 6
                                height: 6
                                radius: 3
                                color: "white"
                                anchors.centerIn: parent
                            }
                        }
                        
                        // 用户信息线条
                        Column {
                            spacing: 1
                            
                            Rectangle {
                                width: 25
                                height: 2
                                radius: 1
                                color: "#ecf0f1"
                            }
                            
                            Rectangle {
                                width: 20
                                height: 1
                                radius: 0.5
                                color: "#bdc3c7"
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 管理图标
    Rectangle {
        id: managementIcon
        anchors.right: folder.right
        anchors.top: folder.top
        anchors.margins: -8
        width: 20
        height: 20
        radius: 10
        color: "#e74c3c"
        border.width: 2
        border.color: "#c0392b"
        
        // 齿轮图标
        Canvas {
            anchors.centerIn: parent
            width: 12
            height: 12
            
            onPaint: {
                var ctx = getContext("2d")
                ctx.clearRect(0, 0, width, height)
                
                var centerX = width / 2
                var centerY = height / 2
                var outerRadius = 5
                var teeth = 6
                
                ctx.fillStyle = "white"
                ctx.beginPath()
                
                for (var i = 0; i < teeth * 2; i++) {
                    var angle = (i * Math.PI) / teeth
                    var radius = (i % 2 === 0) ? outerRadius : outerRadius * 0.7
                    var x = centerX + radius * Math.cos(angle)
                    var y = centerY + radius * Math.sin(angle)
                    
                    if (i === 0) {
                        ctx.moveTo(x, y)
                    } else {
                        ctx.lineTo(x, y)
                    }
                }
                
                ctx.closePath()
                ctx.fill()
                
                // 中心孔
                ctx.fillStyle = "#e74c3c"
                ctx.beginPath()
                ctx.arc(centerX, centerY, 2, 0, 2 * Math.PI)
                ctx.fill()
            }
        }
        
        // 旋转动画
        RotationAnimation {
            target: managementIcon
            running: true
            loops: Animation.Infinite
            from: 0
            to: 360
            duration: 3000
        }
    }
    
    // 底部标签
    Rectangle {
        width: folder.width
        height: 12
        color: "#ecf0f1"
        radius: 2
        anchors.horizontalCenter: folder.horizontalCenter
        anchors.top: folder.bottom
        anchors.topMargin: 8
        
        Text {
            text: "账号管理"
            font.pixelSize: 8
            color: root.secondaryColor
            anchors.centerIn: parent
        }
    }
}
