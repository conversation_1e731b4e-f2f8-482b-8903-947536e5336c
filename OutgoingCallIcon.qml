import QtQuick 2.12

// 呼出图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#3498db"
    property color arrowColor: "#2980b9"
    
    // 电话图标
    Rectangle {
        id: phone
        anchors.centerIn: parent
        width: 50
        height: 65
        color: root.primaryColor
        radius: 20
        
        // 听筒装饰
        Rectangle {
            width: 30
            height: 8
            radius: 4
            color: "white"
            opacity: 0.8
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 8
        }
        
        Rectangle {
            width: 30
            height: 8
            radius: 4
            color: "white"
            opacity: 0.8
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 8
        }
    }
    
    // 向上箭头
    Canvas {
        id: arrowUp
        anchors.horizontalCenter: phone.horizontalCenter
        anchors.bottom: phone.top
        anchors.bottomMargin: 5
        width: 30
        height: 20
        
        onPaint: {
            var ctx = getContext("2d")
            ctx.clearRect(0, 0, width, height)
            ctx.strokeStyle = root.arrowColor
            ctx.lineWidth = 4
            ctx.lineCap = "round"
            ctx.lineJoin = "round"
            
            // 箭头线条
            ctx.beginPath()
            ctx.moveTo(5, 15)
            ctx.lineTo(15, 5)
            ctx.lineTo(25, 15)
            ctx.stroke()
            
            // 箭头杆
            ctx.beginPath()
            ctx.moveTo(15, 20)
            ctx.lineTo(15, 8)
            ctx.stroke()
        }
    }
    
    // 拨号动画效果
    Repeater {
        model: 3
        
        Rectangle {
            width: phone.width + (index + 1) * 15
            height: phone.height + (index + 1) * 15
            radius: width / 2
            color: "transparent"
            border.width: 2
            border.color: root.primaryColor
            opacity: 0.4 - index * 0.1
            anchors.centerIn: phone
            
            SequentialAnimation {
                running: true
                loops: Animation.Infinite
                
                NumberAnimation {
                    target: parent
                    property: "scale"
                    from: 0.8
                    to: 1.2
                    duration: 1000 + index * 200
                }
                
                NumberAnimation {
                    target: parent
                    property: "scale"
                    from: 1.2
                    to: 0.8
                    duration: 1000 + index * 200
                }
            }
        }
    }
}
