import QtQuick 2.12

// 网络设置图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#27ae60"
    property color secondaryColor: "#229954"
    property color signalColor: "#2ecc71"
    
    // 路由器/网络设备
    Rectangle {
        id: router
        anchors.centerIn: parent
        width: 60
        height: 40
        color: root.primaryColor
        radius: 8
        border.width: 2
        border.color: root.secondaryColor
        
        // 设备指示灯
        Row {
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 8
            spacing: 4
            
            Repeater {
                model: 3
                
                Rectangle {
                    width: 4
                    height: 4
                    radius: 2
                    color: index === 0 ? "#e74c3c" : (index === 1 ? "#f1c40f" : "#2ecc71")
                    
                    SequentialAnimation {
                        running: true
                        loops: Animation.Infinite
                        
                        NumberAnimation {
                            target: parent
                            property: "opacity"
                            from: 1.0
                            to: 0.3
                            duration: 800 + index * 200
                        }
                        
                        NumberAnimation {
                            target: parent
                            property: "opacity"
                            from: 0.3
                            to: 1.0
                            duration: 800 + index * 200
                        }
                    }
                }
            }
        }
        
        // 网络端口
        Row {
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 6
            spacing: 3
            
            Repeater {
                model: 4
                Rectangle {
                    width: 8
                    height: 4
                    radius: 1
                    color: "#34495e"
                    border.width: 1
                    border.color: "#2c3e50"
                }
            }
        }
    }
    
    // 天线
    Rectangle {
        width: 3
        height: 20
        color: root.secondaryColor
        radius: 1.5
        anchors.horizontalCenter: router.horizontalCenter
        anchors.bottom: router.top
        anchors.bottomMargin: -2
    }
    
    Rectangle {
        width: 3
        height: 15
        color: root.secondaryColor
        radius: 1.5
        anchors.right: router.right
        anchors.bottom: router.top
        anchors.bottomMargin: -2
        anchors.rightMargin: 8
    }
    
    // WiFi信号波
    Repeater {
        model: 3
        
        Rectangle {
            width: router.width + (index + 1) * 15
            height: router.height + (index + 1) * 10
            radius: width / 2
            color: "transparent"
            border.width: 2
            border.color: root.signalColor
            opacity: 0.6 - index * 0.15
            anchors.centerIn: router
            
            SequentialAnimation {
                running: true
                loops: Animation.Infinite
                
                NumberAnimation {
                    target: parent
                    property: "scale"
                    from: 0.8
                    to: 1.1
                    duration: 1500 + index * 300
                }
                
                NumberAnimation {
                    target: parent
                    property: "scale"
                    from: 1.1
                    to: 0.8
                    duration: 1500 + index * 300
                }
            }
        }
    }
    
    // 网络连接设备
    Row {
        anchors.horizontalCenter: router.horizontalCenter
        anchors.top: router.bottom
        anchors.topMargin: 15
        spacing: 12
        
        Repeater {
            model: 2
            
            Rectangle {
                width: 15
                height: 12
                color: "#3498db"
                radius: 2
                
                Rectangle {
                    width: parent.width - 4
                    height: parent.height - 4
                    color: "#2980b9"
                    radius: 1
                    anchors.centerIn: parent
                }
                
                // 连接线
                Rectangle {
                    width: 2
                    height: 15
                    color: "#95a5a6"
                    anchors.horizontalCenter: parent.horizontalCenter
                    anchors.bottom: parent.top
                }
            }
        }
    }
    
    // 设置图标
    Rectangle {
        width: 16
        height: 16
        radius: 8
        color: "#f39c12"
        anchors.right: router.right
        anchors.top: router.top
        anchors.margins: -6
        
        Text {
            text: "⚙"
            color: "white"
            font.pixelSize: 10
            anchors.centerIn: parent
        }
    }
}
