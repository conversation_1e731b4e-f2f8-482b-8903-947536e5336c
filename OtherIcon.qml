import QtQuick 2.12

// 其他功能图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#95a5a6"
    property color secondaryColor: "#7f8c8d"
    property color accentColor: "#3498db"
    
    // 主容器
    Rectangle {
        id: container
        anchors.centerIn: parent
        width: 70
        height: 70
        color: root.primaryColor
        radius: 35
        border.width: 3
        border.color: root.secondaryColor
        
        // 三个点图标
        Row {
            anchors.centerIn: parent
            spacing: 8
            
            Repeater {
                model: 3
                
                Rectangle {
                    width: 12
                    height: 12
                    radius: 6
                    color: root.accentColor
                    
                    SequentialAnimation {
                        running: true
                        loops: Animation.Infinite
                        
                        NumberAnimation {
                            target: parent
                            property: "scale"
                            from: 1.0
                            to: 1.4
                            duration: 600 + index * 200
                        }
                        
                        NumberAnimation {
                            target: parent
                            property: "scale"
                            from: 1.4
                            to: 1.0
                            duration: 600 + index * 200
                        }
                        
                        PauseAnimation {
                            duration: 400
                        }
                    }
                }
            }
        }
    }
    
    // 功能选项环绕
    Repeater {
        model: 6
        
        Rectangle {
            id: optionDot
            width: 8
            height: 8
            radius: 4
            color: root.secondaryColor
            
            property real angle: index * 60
            property real distance: 45
            
            x: container.x + container.width/2 + distance * Math.cos(angle * Math.PI / 180) - width/2
            y: container.y + container.height/2 + distance * Math.sin(angle * Math.PI / 180) - height/2
            
            SequentialAnimation {
                running: true
                loops: Animation.Infinite
                
                NumberAnimation {
                    target: optionDot
                    property: "opacity"
                    from: 0.3
                    to: 1.0
                    duration: 800 + index * 100
                }
                
                NumberAnimation {
                    target: optionDot
                    property: "opacity"
                    from: 1.0
                    to: 0.3
                    duration: 800 + index * 100
                }
            }
        }
    }
    
    // 连接线
    Repeater {
        model: 6
        
        Rectangle {
            width: 2
            height: 20
            color: root.secondaryColor
            opacity: 0.4
            
            property real angle: index * 60
            property real distance: 25
            
            x: container.x + container.width/2 + distance * Math.cos(angle * Math.PI / 180) - width/2
            y: container.y + container.height/2 + distance * Math.sin(angle * Math.PI / 180) - height/2
            
            rotation: angle + 90
            transformOrigin: Item.Center
        }
    }
    
    // 中心装饰
    Rectangle {
        width: 20
        height: 20
        radius: 10
        color: "white"
        border.width: 2
        border.color: root.accentColor
        anchors.centerIn: container
        
        Rectangle {
            width: 8
            height: 8
            radius: 4
            color: root.accentColor
            anchors.centerIn: parent
        }
    }
    
    // 旋转动画
    RotationAnimation {
        target: container
        running: true
        loops: Animation.Infinite
        from: 0
        to: 360
        duration: 8000
    }
    
    // 底部标签
    Text {
        text: "更多功能"
        font.pixelSize: 10
        color: root.secondaryColor
        anchors.horizontalCenter: container.horizontalCenter
        anchors.top: container.bottom
        anchors.topMargin: 15
    }
}
