import QtQuick 2.12

// 设备温度图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color coldColor: "#3498db"
    property color normalColor: "#27ae60"
    property color warmColor: "#f1c40f"
    property color hotColor: "#e74c3c"
    property real temperature: 45 // 当前温度
    
    // 温度计主体
    Rectangle {
        id: thermometer
        anchors.centerIn: parent
        width: 20
        height: 80
        color: "#ecf0f1"
        radius: 10
        border.width: 2
        border.color: "#bdc3c7"
        
        // 温度计刻度
        Column {
            anchors.right: parent.left
            anchors.rightMargin: 5
            anchors.verticalCenter: parent.verticalCenter
            spacing: 12
            
            Repeater {
                model: ["100°", "75°", "50°", "25°", "0°"]
                
                Text {
                    text: modelData
                    font.pixelSize: 8
                    color: "#7f8c8d"
                }
            }
        }
        
        // 刻度线
        Column {
            anchors.left: parent.right
            anchors.leftMargin: 2
            anchors.verticalCenter: parent.verticalCenter
            spacing: 15
            
            Repeater {
                model: 5
                Rectangle {
                    width: 6
                    height: 1
                    color: "#bdc3c7"
                }
            }
        }
        
        // 温度液体
        Rectangle {
            id: temperatureFluid
            anchors.bottom: parent.bottom
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottomMargin: 3
            width: parent.width - 6
            height: (parent.height - 20) * (root.temperature / 100)
            radius: width / 2
            color: {
                if (root.temperature < 25) return root.coldColor
                else if (root.temperature < 50) return root.normalColor
                else if (root.temperature < 75) return root.warmColor
                else return root.hotColor
            }
            
            // 温度变化动画
            Behavior on height {
                NumberAnimation {
                    duration: 1000
                    easing.type: Easing.OutQuad
                }
            }
            
            Behavior on color {
                ColorAnimation {
                    duration: 500
                }
            }
        }
        
        // 温度计底部球体
        Rectangle {
            width: parent.width + 4
            height: width
            radius: width / 2
            color: temperatureFluid.color
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
            anchors.bottomMargin: -2
        }
    }
    
    // 温度数值显示
    Rectangle {
        width: 50
        height: 25
        color: "#34495e"
        radius: 5
        anchors.horizontalCenter: thermometer.horizontalCenter
        anchors.bottom: thermometer.top
        anchors.bottomMargin: 10
        
        Text {
            text: Math.round(root.temperature) + "°C"
            color: "white"
            font.pixelSize: 12
            font.bold: true
            anchors.centerIn: parent
        }
        
        // 警告指示
        Rectangle {
            visible: root.temperature > 80
            width: 8
            height: 8
            radius: 4
            color: root.hotColor
            anchors.right: parent.right
            anchors.top: parent.top
            anchors.margins: 2
            
            SequentialAnimation {
                running: parent.visible
                loops: Animation.Infinite
                
                NumberAnimation {
                    target: parent
                    property: "opacity"
                    from: 1.0
                    to: 0.3
                    duration: 500
                }
                
                NumberAnimation {
                    target: parent
                    property: "opacity"
                    from: 0.3
                    to: 1.0
                    duration: 500
                }
            }
        }
    }
    
    // 散热风扇图标
    Rectangle {
        id: fan
        anchors.right: thermometer.right
        anchors.bottom: thermometer.bottom
        anchors.margins: -15
        width: 25
        height: 25
        radius: 12.5
        color: "transparent"
        border.width: 2
        border.color: "#95a5a6"
        
        // 风扇叶片
        Repeater {
            model: 4
            
            Rectangle {
                width: 8
                height: 2
                color: "#95a5a6"
                radius: 1
                anchors.centerIn: parent
                rotation: index * 90
                transformOrigin: Item.Center
            }
        }
        
        // 风扇旋转动画
        RotationAnimation {
            target: fan
            running: root.temperature > 60
            loops: Animation.Infinite
            from: 0
            to: 360
            duration: 1000
        }
    }
    
    // 温度变化模拟
    Timer {
        running: true
        repeat: true
        interval: 3000
        onTriggered: {
            root.temperature = Math.random() * 100
        }
    }
}
