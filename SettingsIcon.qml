import QtQuick 2.12

// 常用设置图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#95a5a6"
    property color secondaryColor: "#7f8c8d"
    property color accentColor: "#3498db"
    
    // 齿轮主体
    Canvas {
        id: gear
        anchors.centerIn: parent
        width: 60
        height: 60
        
        onPaint: {
            var ctx = getContext("2d")
            ctx.clearRect(0, 0, width, height)
            
            var centerX = width / 2
            var centerY = height / 2
            var outerRadius = 28
            var innerRadius = 12
            var teeth = 8
            
            ctx.fillStyle = root.primaryColor
            ctx.beginPath()
            
            for (var i = 0; i < teeth * 2; i++) {
                var angle = (i * Math.PI) / teeth
                var radius = (i % 2 === 0) ? outerRadius : outerRadius * 0.8
                var x = centerX + radius * Math.cos(angle)
                var y = centerY + radius * Math.sin(angle)
                
                if (i === 0) {
                    ctx.moveTo(x, y)
                } else {
                    ctx.lineTo(x, y)
                }
            }
            
            ctx.closePath()
            ctx.fill()
            
            // 内圆
            ctx.fillStyle = "white"
            ctx.beginPath()
            ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI)
            ctx.fill()
            
            // 中心孔
            ctx.fillStyle = root.secondaryColor
            ctx.beginPath()
            ctx.arc(centerX, centerY, 6, 0, 2 * Math.PI)
            ctx.fill()
        }
        
        // 旋转动画
        RotationAnimation {
            target: gear
            running: true
            loops: Animation.Infinite
            from: 0
            to: 360
            duration: 4000
        }
    }
    
    // 小齿轮
    Canvas {
        id: smallGear
        anchors.right: gear.right
        anchors.bottom: gear.bottom
        anchors.margins: -8
        width: 25
        height: 25
        
        onPaint: {
            var ctx = getContext("2d")
            ctx.clearRect(0, 0, width, height)
            
            var centerX = width / 2
            var centerY = height / 2
            var outerRadius = 11
            var teeth = 6
            
            ctx.fillStyle = root.accentColor
            ctx.beginPath()
            
            for (var i = 0; i < teeth * 2; i++) {
                var angle = (i * Math.PI) / teeth
                var radius = (i % 2 === 0) ? outerRadius : outerRadius * 0.7
                var x = centerX + radius * Math.cos(angle)
                var y = centerY + radius * Math.sin(angle)
                
                if (i === 0) {
                    ctx.moveTo(x, y)
                } else {
                    ctx.lineTo(x, y)
                }
            }
            
            ctx.closePath()
            ctx.fill()
            
            // 中心孔
            ctx.fillStyle = "white"
            ctx.beginPath()
            ctx.arc(centerX, centerY, 3, 0, 2 * Math.PI)
            ctx.fill()
        }
        
        // 反向旋转动画
        RotationAnimation {
            target: smallGear
            running: true
            loops: Animation.Infinite
            from: 360
            to: 0
            duration: 3000
        }
    }
    
    // 设置标签
    Row {
        anchors.horizontalCenter: gear.horizontalCenter
        anchors.top: gear.bottom
        anchors.topMargin: 10
        spacing: 2
        
        Repeater {
            model: 3
            Rectangle {
                width: 15
                height: 3
                radius: 1.5
                color: root.secondaryColor
            }
        }
    }
}
