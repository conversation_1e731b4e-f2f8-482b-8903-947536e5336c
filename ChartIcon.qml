import QtQuick 2.12

// 图表图标控件
Rectangle {
    id: root
    width: 120
    height: 120
    color: "transparent"
    
    property color primaryColor: "#3498db"
    property color secondaryColor: "#2980b9"
    property color accentColor: "#e74c3c"
    
    // 图表背景
    Rectangle {
        id: chartBackground
        anchors.centerIn: parent
        width: 80
        height: 60
        color: "white"
        border.width: 2
        border.color: root.primaryColor
        radius: 5
        
        // 网格线
        Column {
            anchors.fill: parent
            anchors.margins: 8
            spacing: (parent.height - 16) / 4
            
            Repeater {
                model: 4
                Rectangle {
                    width: parent.width
                    height: 1
                    color: "#ecf0f1"
                }
            }
        }
        
        Row {
            anchors.fill: parent
            anchors.margins: 8
            spacing: (parent.width - 16) / 5
            
            Repeater {
                model: 5
                Rectangle {
                    width: 1
                    height: parent.height
                    color: "#ecf0f1"
                }
            }
        }
        
        // 柱状图
        Row {
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 8
            anchors.horizontalCenter: parent.horizontalCenter
            spacing: 6
            
            Repeater {
                model: [0.3, 0.7, 0.5, 0.9, 0.4]
                
                Rectangle {
                    width: 8
                    height: (chartBackground.height - 16) * modelData
                    color: index === 3 ? root.accentColor : root.primaryColor
                    radius: 2
                    
                    SequentialAnimation {
                        running: true
                        loops: Animation.Infinite
                        
                        NumberAnimation {
                            target: parent
                            property: "height"
                            from: (chartBackground.height - 16) * modelData
                            to: (chartBackground.height - 16) * modelData * 1.2
                            duration: 1000 + index * 200
                        }
                        
                        NumberAnimation {
                            target: parent
                            property: "height"
                            from: (chartBackground.height - 16) * modelData * 1.2
                            to: (chartBackground.height - 16) * modelData
                            duration: 1000 + index * 200
                        }
                    }
                }
            }
        }
    }
    
    // 标题
    Rectangle {
        width: chartBackground.width
        height: 15
        color: root.secondaryColor
        radius: 3
        anchors.horizontalCenter: chartBackground.horizontalCenter
        anchors.bottom: chartBackground.top
        anchors.bottomMargin: 5
        
        Text {
            text: "数据统计"
            color: "white"
            font.pixelSize: 8
            font.bold: true
            anchors.centerIn: parent
        }
    }
    
    // 趋势箭头
    Canvas {
        id: trendArrow
        anchors.right: chartBackground.right
        anchors.top: chartBackground.top
        anchors.margins: 5
        width: 15
        height: 15
        
        onPaint: {
            var ctx = getContext("2d")
            ctx.clearRect(0, 0, width, height)
            ctx.fillStyle = root.accentColor
            ctx.beginPath()
            ctx.moveTo(0, 15)
            ctx.lineTo(15, 0)
            ctx.lineTo(15, 8)
            ctx.lineTo(8, 8)
            ctx.lineTo(8, 15)
            ctx.closePath()
            ctx.fill()
        }
    }
}
